#ifndef IO_H
#define IO_H

#include <string>
#include "gamestate.h"

class IO {
 public:
  static void parse_command_line(int argc, char* argv[], int& hp,
                                 std::string& mode, std::string& log_file);
  static MoveCommand get_user_input(const std::string& player);
  static void print_map(const GameState& state);
  static void print_help();
  static void print_message(const std::string& msg);
  static void print_end(const GameState& state);

 private:
  // Helper functions for direction visualization
  static char get_direction_symbol(Direction dir);
  static std::string get_direction_arrow(Direction dir);
  static std::string get_tank_symbol(char tank_id, Direction dir);
};

#endif